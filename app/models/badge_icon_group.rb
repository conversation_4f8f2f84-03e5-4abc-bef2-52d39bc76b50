class BadgeIconGroup < ApplicationRecord
  belongs_to :circle, optional: true
  has_many :badge_icons
  has_many :user_roles, through: :badge_icons

  after_update_commit :trigger_user_identity_images_regeneration

  validates_uniqueness_of :circle_id

  def trigger_user_identity_images_regeneration
    # Trigger identity image regeneration for users when badge icon group changes
    if saved_change_to_circle_id?
      user_roles.find_in_batches(batch_size: 250) do |batch|
        batch.each do |user_role|
          RegenerateUserIdentityImagesWorker.perform_async(user_role.user_id)
        end
      end
      Rails.logger.info("Identity image regeneration triggered for users with badge_icon_group #{id} due to circle association change")
    end
  end
end
