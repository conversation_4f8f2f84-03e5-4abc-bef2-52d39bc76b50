class UserVideoFrame < ApplicationRecord
  belongs_to :user
  belongs_to :video_frame

  has_many :user_video_posters, dependent: :destroy

  validates :user_id, presence: true
  validates :video_frame_id, presence: true
  validates :identity_photo_url, presence: true

  scope :active, -> { where(active: true) }

  # Check if identity image needs to be regenerated
  def needs_identity_image_regeneration?
    identity_photo_url.blank? ||
    identity_photo_url.include?('identity.png') || # fallback placeholder
    identity_photo_url.include?('placeholder')
  end
end
