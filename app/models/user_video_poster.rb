# frozen_string_literal: true

class UserVideoPoster < ApplicationRecord
    include AASM

    enum status: {
      pending: 'pending',
      processing: 'processing',
      completed: 'completed',
      failed: 'failed',
    }

    # Set default status
    attribute :status, default: 'pending'

    belongs_to :user_video_frame
    belongs_to :source_video, class_name: 'Video'
    belongs_to :user
    belongs_to :generated_video, class_name: 'Video', optional: true

    # Delegate video_frame access through user_video_frame
    delegate :video_frame, to: :user_video_frame

    validates :user_id, presence: true
    validates :source_video_id, presence: true
    validates :user_video_frame_id, presence: true

    before_create :add_job_id

    # Store error_code in metadata JSON field
    def error_code
      metadata&.dig('error_code')
    end

    def error_code=(value)
      self.metadata = (metadata || {}).merge('error_code' => value)
    end

    def add_job_id
      self.job_id = ULID.generate
    end

    # Instance variable to store callback data temporarily
    attr_accessor :callback_data_for_completion

    # Method to complete with callback data
    def complete_with_callback_data!(callback_data = {})
      self.callback_data_for_completion = callback_data
      complete!
    end

    aasm column: :status, enum: true do
      state :pending, initial: true
      state :processing
      state :completed
      state :failed
      event :process, after_commit: :call_back_to_dm do
        transitions from: :pending, to: :processing
      end

      event :complete, after_commit: :call_back_to_dm do
        before do
          save_generated_video_object(callback_data_for_completion || {})
        end
        transitions from: :processing, to: :completed
      end
  
      event :fail, after_commit: :call_back_to_dm do	
        before do |error_code|	
          generation_failed(error_code)	
        end	
        transitions from: :processing, to: :failed	
      end	
  
      event :mark_as_pending do	
        transitions from: :failed, to: :pending	
      end	
    end	
  
    def call_back_to_dm	
      req_body = { userId: user_id.to_s, data: { user_id: user_id, video_poster: get_json } }	
      req_url = "#{Constants.get_dm_url}/video-posters/status-updated"	
      response = DmUtil.put_request_to_dm(req_url, req_body)	
      if response.code.to_i != 200	
        Honeybadger.notify("Failed to update video poster status in DM. Response code: #{response.code} for data #{req_body}")	
      end	
    end	
  
    def after_completed	
      call_back_to_dm	
    end	
  
    def after_failed	
      call_back_to_dm	
    end	
  
    def generation_failed(error_code = 'unknown')	
      self.error_code = error_code	
    end	
  
    def save_generated_video_object(callback_data = {})
      if generated_video_id.nil?
        # Use callback data if available, otherwise fall back to default URLs
        data = callback_data || {}

        video_attributes = {
          url: "https://ruv-cdn.thecircleapp.in/video-posters/#{job_id}-video.mp4",
          thumbnail_url: "https://a-cdn.thecircleapp.in/production/video-posters/#{job_id}-thumbnail.jpg",
          user_id: user_id,
          status: :processed,
          service: :aws,
        }

        # Add video metadata if available from callback
        video_attributes[:width] = data[:width] || data['width'] if (data[:width] || data['width']).present?
        video_attributes[:height] = data[:height] || data['height'] if (data[:height] || data['height']).present?
        video_attributes[:duration] = data[:duration] || data['duration'] if (data[:duration] || data['duration']).present?
        video_attributes[:bitrate] = data[:bitrate] || data['bitrate'] if (data[:bitrate] || data['bitrate']).present?

        video = Video.create!(video_attributes)
        self.generated_video = video
      end
    end

    def get_json
      video = generated_video if completed?
      {
        id: id,
        user_video_frame_id: user_video_frame_id,
        video_frame_id: user_video_frame&.video_frame_id,
        video: video,
        status: status,
        error_code: error_code,
      }
    end
  # Constants based on the commented C++ constants
  FRAME_DIMENSIONS = {
    'portrait' => { width: 360, height: 550 },
    'landscape' => { width: 360, height: 358 },
    'square' => { width: 360, height: 420 }
  }.freeze

  VIDEO_DIMENSIONS = {
    'portrait' => { width: 198, height: 352 },
    'landscape' => { width: 330, height: 188 },
    'square' => { width: 330, height: 248 }
  }.freeze

  USER_PHOTO_DIMENSIONS = {
    'portrait' => { width: 180, height: 202 },
    'landscape' => { width: 116, height: 130 },
    'square' => { width: 116, height: 130 }
  }.freeze

  # Spacing constants from commented constants
  FRAME_PADDING = 16
  VIDEO_PADDING = 15
  IDENTITY_SPACING = 8
  PROTOCOL_IMAGE_WIDTH = 360  # protocolImageWidth from comments (missing but inferred)
  PROTOCOL_IMAGE_HEIGHT = 86  # protocolImageHeight from comments
  IDENTITY_PLATE_WIDTH = 360
  IDENTITY_PLATE_HEIGHT = 50

  # Identity image dimensions for different orientations
  IDENTITY_IMAGE_DIMENSIONS = {
    'portrait' => { width: 360, height: IDENTITY_PLATE_HEIGHT },
    'landscape' => { width: 360, height: IDENTITY_PLATE_HEIGHT },
    'square' => { width: 360, height: IDENTITY_PLATE_HEIGHT }
  }.freeze

  # Identity image styling constants
  IDENTITY_TEXT_PADDING = 10
  IDENTITY_NAME_FONT_SIZE_LARGE = 30
  IDENTITY_NAME_FONT_SIZE_MEDIUM = 26
  IDENTITY_NAME_FONT_SIZE_SMALL = 22
  IDENTITY_NAME_FONT_SIZE_MIN = 18
  IDENTITY_BADGE_FONT_SIZE = 20
  IDENTITY_LEFT_PADDING_LANDSCAPE = 132  # Based on template: user photo width + padding

  # Font size thresholds for name length
  IDENTITY_NAME_LENGTH_MEDIUM_THRESHOLD = 12
  IDENTITY_NAME_LENGTH_SMALL_THRESHOLD = 16
  IDENTITY_NAME_LENGTH_MIN_THRESHOLD = 20

  def lambda_payload
    mode = get_video_mode
    frame_dimensions = get_frame_dimensions(mode)
    video_dimensions = get_video_dimensions(mode)
    user_photo_dimensions = get_user_photo_dimensions(mode)

    {
      "payload": [
        get_background_data(frame_dimensions),
        get_protocol_data,
        get_video_data(video_dimensions, frame_dimensions),
        get_user_photo_data(user_photo_dimensions, frame_dimensions),
        get_identity_data(frame_dimensions)
      ],
      "frame_height": frame_dimensions[:height],
      "frame_width": frame_dimensions[:width],
      "callback_url": "#{Constants.get_api_host}/video-posters",
      "job_id": job_id
    }
  end

  private

  def get_video_mode
    video_frame&.video_type || 'portrait'
  end

  def get_frame_dimensions(mode)
    dimensions = FRAME_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_video_dimensions(mode)
    dimensions = VIDEO_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_user_photo_dimensions(mode)
    dimensions = USER_PHOTO_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_background_data(frame_dimensions)
    {
      "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/tysrcp.png",
      "width": frame_dimensions[:width],
      "height": frame_dimensions[:height],
      "x": 0,
      "y": 0,
      "type": "photo"
    }
  end

  def get_video_data(video_dimensions, frame_dimensions)
    mode = get_video_mode

    # Calculate video position based on mode
    x_position, y_position = calculate_video_position(mode, video_dimensions, frame_dimensions)

    video_url = source_video&.source_url || source_video&.url
    # Fallback URL if no video URL is available
    video_url ||= "https://ruv-cdn.thecircleapp.in/assets01/58be1c4819d4d7db78989b7cddc7ec82.mp4"

    {
      "url": video_url,
      "width": video_dimensions[:width],
      "height": video_dimensions[:height],
      "x": x_position,
      "y": y_position,
      "type": "video",
      "border": {
        "radius": 20,
        "size": 1,
        "color":  "#ff0000"
      }
    }
  end

  def get_user_photo_data(user_photo_dimensions, frame_dimensions)
    mode = get_video_mode

    # Calculate user photo position based on mode
    x_position, y_position = calculate_user_photo_position(mode, user_photo_dimensions, frame_dimensions)

    user_photo_url = user&.poster_photo&.url
    # Fallback URL if no user photo is available
    user_photo_url ||= "https://a-cdn.thecircleapp.in/production/photos/41/60ac97b3-6911-4816-b393-86b5086b976c.png"

    {
      "url": user_photo_url,
      "width": user_photo_dimensions[:width],
      "height": user_photo_dimensions[:height],
      "x": x_position,
      "y": y_position,
      "type": "photo"
    }
  end

  def get_protocol_data
    {
      "url": get_protocl_photo_url || "https://a-cdn.thecircleapp.in/production/user-protocol-photos/protocol-298110-1748337600498.png",
      "width": PROTOCOL_IMAGE_WIDTH,
      "height": PROTOCOL_IMAGE_HEIGHT,
      "x": FRAME_PADDING,
      "y": FRAME_PADDING,
      "type": "photo"
    }
  end

  def get_identity_data(frame_dimensions)
    identity_url = user_video_frame&.identity_photo_url
    # Fallback URL if no identity photo is available
    identity_url ||= "https://a-cdn.thecircleapp.in/production/user-protocol-photos/identity.png"

    {
      "url": identity_url,
      "width": IDENTITY_PLATE_WIDTH,
      "height": IDENTITY_PLATE_HEIGHT,
      "x": 0,
      "y": frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT,
      "type": "photo"
    }
  end

  # Position calculation methods
  def calculate_video_position(mode, video_dimensions, frame_dimensions)
    case mode
    when 'portrait'
      x = frame_dimensions[:width] - FRAME_PADDING - video_dimensions[:width]
      y = frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT - IDENTITY_SPACING - video_dimensions[:height]
    when 'square'
      x = frame_dimensions[:width] - video_dimensions[:width] - VIDEO_PADDING
      y = frame_dimensions[:height] - video_dimensions[:height] - IDENTITY_PLATE_HEIGHT - VIDEO_PADDING
    when 'landscape'
      x = frame_dimensions[:width] - video_dimensions[:width] - VIDEO_PADDING
      y = frame_dimensions[:height] - video_dimensions[:height] - IDENTITY_PLATE_HEIGHT - 9
    else
      raise ArgumentError, "Invalid video mode: #{mode}"
    end
    [x, y]
  end

  def calculate_user_photo_position(mode, user_photo_dimensions, frame_dimensions)
    case mode
    when 'portrait'
      x = 0
      y = frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT - user_photo_dimensions[:height]
    when 'square', 'landscape'
      x = 0
      y = frame_dimensions[:height] - user_photo_dimensions[:height]
    else
      raise ArgumentError, "Invalid video mode: #{mode}"
    end
    [x, y]
  end

  def get_protocl_photo_url
    UserPosterLayout.where(entity: user).last&.video_frame_protocol_photo_url
  end


end
