class VideoPosterShare < ApplicationRecord
  belongs_to :video_creative
  belongs_to :user
  after_create_commit :index_for_posters_feed

  enum method: {
    whatsapp: "whatsapp",
    external_share: "external_share",
    download: "download",
    unknown: "unknown"
  }

  private

  def index_for_posters_feed
    IndexCreativesForPostersFeed.perform_async("video_creative_#{video_creative.id}")
  end

end
