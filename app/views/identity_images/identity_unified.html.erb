<!DOCTYPE html>
<html lang="te">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Badge Template</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@400;500;700&family=Anek+Telugu:wght@400;500;700&display=swap');

        :root {
            --container-width: <%= container_width %>px;
            --container-height: <%= container_height %>px;
            --user-photo-width: <%= user_photo_width %>px;
            --text-padding: <%= text_padding %>px;
            --left-padding: <%= left_padding %>px;
            --name-font-size: <%= name_font_size %>px;
            --badge-font-size: <%= badge_font_size %>px;
            --name-font-family: '<%= name_font_family %>';
            --badge-font-family: '<%= badge_font_family %>';
        }

        #body {
            background: black;
        }

        #top-outer-container {
            width: var(--container-width);
            height: var(--container-height);
            position: relative;
            margin: 20px auto;
            background: white;
            display: flex;
            align-items: center;
            flex-direction: row;
            justify-content: center;
            padding: var(--text-padding);
            box-sizing: border-box;
        }

        #user-photo-placeholder {
            width: var(--user-photo-width);
            height: var(--container-height);
            background: white;
        }

        #outer-container {
            width: var(--container-width);
            height: var(--container-height);
            position: relative;
            background: white;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            padding: var(--text-padding);
            box-sizing: border-box;
        }

        #user-name {
            font-family: var(--name-font-family), 'Noto Sans Telugu', sans-serif;
            font-size: var(--name-font-size);
            font-weight: 700;
            color: #333;
            margin: 0;
            line-height: 1.2;
            word-wrap: break-word;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        #badge-description {
            font-family: var(--badge-font-family), 'Noto Sans Telugu', sans-serif;
            font-size: var(--badge-font-size);
            font-weight: 500;
            color: #666;
            margin: 0;
            line-height: 1.3;
            word-wrap: break-word;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        body {
            background: white;
            font-family: 'Noto Sans Telugu', sans-serif;
            margin: 0;
            padding: 0;
        }

        * {
            box-sizing: border-box;
        }
    </style>
</head>

<body id="body">
    <div id="top-outer-container">
        <% if user_photo_width > 0 %>
        <div id="user-photo-placeholder"></div>
        <% end %>
        <div id="outer-container">
            <h1 id="user-name"><%= user_name %></h1>
            <% if badge_description.present? %>
            <p id="badge-description"><%= badge_description %></p>
            <% end %>
        </div>
    </div>
</body>

</html>
