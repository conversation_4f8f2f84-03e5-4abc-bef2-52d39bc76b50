# frozen_string_literal: true

class RegenerateUserIdentityImagesWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  
  sidekiq_options queue: :video_posters_generation, retry: 3, lock: :until_and_while_executing, on_conflict: :log
  
  sidekiq_throttle(
    concurrency: {
      limit: 5,
    },
    threshold: {
      limit: 20,
      period: 5.seconds,
    }
  )

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("RegenerateUserIdentityImagesWorker retries exhausted: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })
    
    user = User.find_by_id(user_id)
    return if user.blank?

    # Get all active video frames
    video_frames = VideoFrame.where(active: true)
    return if video_frames.empty?
    
    # Store old frames to mark inactive later
    old_frames_to_deactivate = []
    new_frames_created = []
    
    # Generate new identity images for each video frame type
    video_frames.each do |video_frame|
      begin
        # Get existing active frame for this user and video frame
        existing_frame = UserVideoFrame.find_by(user: user, video_frame: video_frame, active: true)
        
        # Generate new identity image
        identity_image_url = generate_identity_image(user, video_frame)
        
        # Create new user video frame with the generated identity image URL
        new_user_video_frame = UserVideoFrame.create!(
          user: user,
          video_frame: video_frame,
          active: true,
          identity_photo_url: identity_image_url
        )
        
        new_frames_created << new_user_video_frame
        
        # Add existing frame to deactivation list if it exists
        old_frames_to_deactivate << existing_frame if existing_frame.present?
        
        Rails.logger.info("New identity image generated for user #{user_id}, video_frame #{video_frame.id}")
        
      rescue StandardError => e
        # If generation fails for this frame, log error but continue with other frames
        Honeybadger.notify(e, context: { user_id: user_id, video_frame_id: video_frame.id })
        Rails.logger.error("Failed to generate identity image for user #{user_id}, video_frame #{video_frame.id}: #{e.message}")
        
        # Remove any new frame created for this video_frame if it exists
        new_frames_created.select { |frame| frame.video_frame_id == video_frame.id }.each(&:destroy)
        new_frames_created.reject! { |frame| frame.video_frame_id == video_frame.id }
      end
    end
    
    # Only mark old frames as inactive if we successfully created new ones
    if new_frames_created.any?
      old_frames_to_deactivate.each do |old_frame|
        old_frame.update!(active: false)
        Rails.logger.info("Marked old identity image as inactive for user #{user_id}, video_frame #{old_frame.video_frame_id}")
      end
      
      Rails.logger.info("Identity images regeneration completed for user #{user_id}. Created #{new_frames_created.count} new frames, deactivated #{old_frames_to_deactivate.count} old frames")
    else
      Rails.logger.error("No new identity images were created for user #{user_id}. Keeping existing frames active.")
    end
    
  rescue StandardError => e
    Honeybadger.notify(e, context: { user_id: user_id })
    Rails.logger.error("RegenerateUserIdentityImagesWorker failed: #{e.message}")
    raise
  end

  private

  def generate_identity_image(user, video_frame)
    # Get user data for identity image
    user_name = user.name
    badge_description = user.get_badge_role&.get_description
    
    # Get font information from video frame
    font = video_frame.font
    name_font_family = font.name_font
    badge_font_family = font.badge_font
    
    # Generate HTML content
    html = generate_html(
      template_name: 'identity_unified',
      video_type: video_frame.video_type,
      user_name: user_name,
      badge_description: badge_description,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family
    )
    
    # Capture HTML as image
    uploaded_image = capture_html_as_image(html, '#top-outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    uploaded_image['cdn_url']
  end

  def generate_html(template_name:, video_type:, user_name:, badge_description:, name_font_family:, badge_font_family:)
    # Get dimensions and styling based on video type
    dimensions = UserVideoPoster::IDENTITY_IMAGE_DIMENSIONS[video_type]
    user_photo_dimensions = UserVideoPoster::USER_PHOTO_DIMENSIONS[video_type]

    # Calculate font sizes based on name length with improved logic
    name_font_size = calculate_name_font_size(user_name)

    # Determine user photo width based on video type (0px for portrait, actual width for landscape)
    user_photo_width = video_type == 'portrait' ? 0 : user_photo_dimensions[:width] + 50

    locals = {
      container_width: dimensions[:width]*2,
      container_height: dimensions[:height]*2,
      text_padding: UserVideoPoster::IDENTITY_TEXT_PADDING,
      name_font_size: name_font_size,
      badge_font_size: UserVideoPoster::IDENTITY_BADGE_FONT_SIZE,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family,
      user_name: user_name,
      badge_description: badge_description,
      user_photo_width: user_photo_width,
      left_padding: UserVideoPoster::IDENTITY_LEFT_PADDING_LANDSCAPE
    }

    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'identity_images', "#{template_name}.html.erb")),
      locals: locals
    )
  end

  def calculate_name_font_size(user_name)
    name_length = user_name.length

    case name_length
    when 0..UserVideoPoster::IDENTITY_NAME_LENGTH_MEDIUM_THRESHOLD
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_LARGE
    when (UserVideoPoster::IDENTITY_NAME_LENGTH_MEDIUM_THRESHOLD + 1)..UserVideoPoster::IDENTITY_NAME_LENGTH_SMALL_THRESHOLD
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_MEDIUM
    when (UserVideoPoster::IDENTITY_NAME_LENGTH_SMALL_THRESHOLD + 1)..UserVideoPoster::IDENTITY_NAME_LENGTH_MIN_THRESHOLD
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_SMALL
    else
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_MIN
    end
  end
end
