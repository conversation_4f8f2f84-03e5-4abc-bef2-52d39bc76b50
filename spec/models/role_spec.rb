require 'rails_helper'

RSpec.describe Role, type: :model do
  # pending "add some examples to (or delete) #{__FILE__}"
  describe Role, :type => :model do
    context 'validate model ' do
      it "is valid with valid attributes" do
        @role = FactoryBot.create(:role)
        expect(@role).to be_valid
      end

      it "is not valid without a name" do
        @role = FactoryBot.create(:role)
        @role.name = nil
        expect(@role).not_to be_valid
      end

      it "is not valid without a quota_type" do
        @role = FactoryBot.create(:role)
        @role.quota_type = nil
        expect(@role).not_to be_valid
      end

      it "is not valid without a grade_level" do
        @role = FactoryBot.create(:role)
        @role.grade_level = nil
        expect(@role).not_to be_valid
      end

      it "is not valid without a parent_circle_level and parent_circle_id" do
        @role = FactoryBot.create(:role)
        @role.parent_circle_level = nil
        @role.parent_circle_id = nil
        expect(@role).not_to be_valid
      end
    end

  end

  describe "validate name" do
    context "it is valid with letters" do
      it "valid with telugu letters" do
        @role = FactoryBot.create(:role)
        @role.name = "జిల్లా కలెక్టర్"
        expect(@role).to be_valid
      end

      it "valid with english letters" do
        @role = FactoryBot.create(:role)
        @role.name = "MLA OFFICE"
        expect(@role).to be_valid
      end

      it 'valid with hyphen in between' do
        @role = FactoryBot.create(:role)
        @role.name = "MLA-OFFICE"
        expect(@role).to be_valid
      end

      it "not valid with numerics" do
        @role = FactoryBot.create(:role)
        @role.name = "123"
        expect(@role).not_to be_valid
      end

      it "not valid with alphanumeric" do
        @role = FactoryBot.create(:role)
        @role.name = "leader1"
        expect(@role).to be_valid
      end
      it "not valid with special characters" do
        @role = FactoryBot.create(:role)
        @role.name = "leader@123"
        expect(@role).not_to be_valid
      end

      it "not valid with blank" do
        @role = FactoryBot.create(:role)
        @role.name = ""
        expect(@role).not_to be_valid
      end

      it "not valid with empty space" do
        @role = FactoryBot.create(:role)
        @role.name = " "
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'validate quota type and quota value' do
    context "should follow the constraints" do
      it "is in_valid with quota_type nil value" do
        @role = FactoryBot.create(:role)
        @role.quota_type = nil
        @role.quota_value = nil
        expect(@role).not_to be_valid
      end

      it "is valid with quota_value nil value" do
        @role = FactoryBot.create(:role)
        @role.quota_type = :no_limit
        @role.quota_value = nil
        expect(@role).to be_valid
      end

      it "is valid with a value" do
        @role = FactoryBot.create(:role)
        expect(@role).to be_valid
      end

    end
  end

  describe 'validate badge' do
    context "should accept only boolean value" do
      it "is valid with bool value" do
        @role = FactoryBot.create(:role)
        @role.has_badge = false
        @role.badge_icon = nil
        @role.badge_color = nil
        @role.badge_ring = nil
        expect(@role).to be_valid
      end

      it "is not valid with nil value" do
        @role = FactoryBot.create(:role)
        @role.has_badge = nil
        expect(@role).not_to be_valid
      end

      it "is not valid with blank" do
        @role = FactoryBot.create(:role)
        @role.has_badge = ""
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'validate badge ring and badge color' do
    it "should be valid with has badge true" do
      @role = FactoryBot.create(:role)
      expect(@role).to be_valid
    end

    it "should not be valid with nil value" do
      @role = FactoryBot.create(:role)
      @role.badge_ring = nil
      @role.badge_color = nil
      expect(@role).not_to be_valid
    end

    it "should not be valid with badge ring nil value" do
      @role = FactoryBot.create(:role)
      @role.badge_ring = nil
      expect(@role).not_to be_valid
    end

    it "should not be valid with badge color nil value" do
      @role = FactoryBot.create(:role)
      @role.badge_color = nil
      expect(@role).not_to be_valid
    end

    it "should be valid with has badge false" do
      @role = FactoryBot.create(:role)
      @role.has_badge = false
      @role.badge_ring = nil
      @role.badge_color = nil
      expect(@role).to be_valid
    end
  end

  describe 'validate grade value' do
    context "should be in given keys only" do
      it "should be valid" do
        @role = FactoryBot.create(:role)
        @role.grade_level = :grade_3
        expect(@role).to be_valid
      end

      it "should not be valid with nil value" do
        @role = FactoryBot.create(:role)
        @role.grade_level = nil
        expect(@role).not_to be_valid
      end

      it "should not be valid with empty space" do
        @role = FactoryBot.create(:role)
        @role.grade_level = " "
        expect(@role).not_to be_valid
      end
    end
  end

  describe "validate parent circle level and parent circle id" do
    context "should be in given list only" do
      it "should be valid" do
        @role = FactoryBot.create(:role)
        @role.parent_circle_level = :private
        expect(@role).to be_valid
      end

      it "should not be valid with space" do
        @role = FactoryBot.create(:role)
        @role.parent_circle_level = " "
        expect(@role).not_to be_valid
      end

      it "should not be valid with both parent circle level and parent circle id nil" do
        @role = FactoryBot.create(:role)
        @role.parent_circle_level = nil
        @role.parent_circle_id = nil
        expect(@role).not_to be_valid
      end

      it "should not be valid with parent circle level and parent circle id both present" do
        @role = FactoryBot.create(:role)
        @role.parent_circle_level = :private
        @role.parent_circle_id = 1
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'validate has purview' do
    context "should be a boolean value" do
      it "should be valid" do
        @role = FactoryBot.create(:role)
        @role.has_purview = true
        @role.purview_level = :village
        expect(@role).to be_valid
      end

      it "should not be valid with nil value" do
        @role = FactoryBot.create(:role)
        @role.has_purview = nil
        expect(@role).not_to be_valid
      end
    end
  end

  describe "validate purview circle level" do
    context "should be in given list only" do
      it "should be valid" do
        @role = FactoryBot.create(:role)
        @role.has_purview = true
        @role.purview_level = :village
        expect(@role).to be_valid
      end

      it "should be valid with nil value when has purview is false" do
        @role = FactoryBot.create(:role)
        expect(@role).to be_valid
      end

      it "should not be valid with value when has purview is false" do
        @role = FactoryBot.create(:role)
        @role.has_purview = false
        @role.purview_level = :village
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'validate active' do
    context "should be a boolean value" do
      it "should be valid" do
        @role = FactoryBot.create(:role)
        @role.active = true
        expect(@role).to be_valid
      end

      it "should not be valid with nil value" do
        @role = FactoryBot.create(:role)
        @role.active = nil
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'validate has badge icon' do
    context "should be a boolean value" do
      it "should be valid" do
        @role = FactoryBot.create(:role)
        @role.has_badge_icon = true
        expect(@role).to be_valid
      end

      it "should not be valid with nil value" do
        @role = FactoryBot.create(:role)
        @role.has_badge_icon = nil
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'validate permission group id' do
    context 'valid with digits only' do
      before :each do
        @role = FactoryBot.create(:role)
      end
      it 'should not be valid without permission group id' do

        @role.permission_group_id = nil
        expect(@role).not_to be_valid
      end

      it 'should not be valid with permission group id is empty' do
        @role.permission_group_id = ''
        expect(@role).not_to be_valid
      end

      it 'should not be valid with permission group id contains spaces' do
        @role.permission_group_id = ' '
        expect(@role).not_to be_valid
      end

      it 'should not be valid with permission group id contains letters' do
        @role.permission_group_id = Faker::Name.unique.name.gsub(/\W/, '')
        expect(@role).not_to be_valid
      end

      it 'should not be valid with permission group id contains special characters' do
        @role.permission_group_id = Faker::Name.unique.name + '@'
        expect(@role).not_to be_valid
      end

      it 'should be valid with permission group id' do
        expect(@role).to be_valid
      end

      it 'should not be valid with not existing permission group id' do
        @role.permission_group_id = 0
        expect(@role).not_to be_valid
      end
    end
  end

  describe 'test user flush cache' do
    context 'check flush_user_cache' do
      it 'should flush user cache' do
        @role = FactoryBot.create(:role)
        @circle = FactoryBot.create(:circle)
        @user_role = FactoryBot.create(:user_role, role: @role, parent_circle_id: @circle.id)
        @role.save
        @role.flush_user_cache

        expect(Rails.cache.read([User::CACHE_KEY, @user_role.user_id])).to be_nil
      end
    end
  end

  describe 'test get_role_data_for_badge_card' do
    context 'check get_role_data_for_badge_card' do
      it 'should return some hash to be present' do
        @circle = FactoryBot.create(:circle)
        # for badge_icon_id
        @photo = FactoryBot.create(:photo)
        @admin_user = FactoryBot.create(:admin_user)
        @badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: @circle.id)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                        badge_icon_group_id: @badge_icon_group.id)

        @role = FactoryBot.create(:role, has_badge_icon: true)
        expect(@role.get_role_data_for_badge_card(@circle.id)).to be_present
      end
    end
  end

  describe 'test invite card method' do
    context 'check invite_card function is calling or not' do
      it 'should call invite_card job' do
        @role = FactoryBot.create(:role)
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
        @role.update_invite_cards
        expect(GenerateInviteCard.jobs[0]['args']).to eq([@user.id])
      end
    end
  end

  describe 'test if user roles are inactive if role become inactive' do
    context 'check if user roles are inactive if role become inactive' do
      it 'should call update_user_roles function' do
        @role = FactoryBot.create(:role)
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
        @role.update(active: false)
        expect(@user_role.reload.active).to eq(false)
      end
    end
  end

  describe 'test get_badge_ring' do
    context 'should return which type of ring' do
      it 'should return gold color of badge ring' do
        @role = FactoryBot.create(:role, badge_color: :GOLD)
        expect(@role.get_badge_ring).to eq('GOLD_RING')
      end

      it 'should return silver color of badge ring' do
        @role = FactoryBot.create(:role, badge_color: :SILVER)
        expect(@role.get_badge_ring).to eq('SILVER_RING')
      end

      it 'should return no color of badge ring' do
        @role = FactoryBot.create(:role, badge_ring: false)
        expect(@role.get_badge_ring).to eq('NO_RING')
      end

    end
  end

  describe "#trigger_user_identity_images_regeneration" do
    let(:role) { FactoryBot.create(:role) }
    let(:circle) { FactoryBot.create(:circle) }
    let(:user1) { FactoryBot.create(:user) }
    let(:user2) { FactoryBot.create(:user) }
    let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, parent_circle_id: circle.id) }
    let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, parent_circle_id: circle.id) }

    context "when role fields affecting badge descriptions change" do
      it "should trigger identity image regeneration for all users when name changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user1.id)
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user2.id)

        role.update!(name: "Updated Role Name")
      end

      it "should trigger identity image regeneration for all users when display_name_order changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user1.id)
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user2.id)

        role.update!(display_name_order: "role,parent,purview")
      end

      it "should trigger identity image regeneration for all users when active status changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user1.id)
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user2.id)

        role.update!(active: false)
      end

      it "should log the regeneration trigger" do
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for all users with role #{role.id} due to role changes")

        role.update!(name: "Updated Role Name")
      end
    end

    context "when non-badge description fields change" do
      it "should not trigger identity image regeneration when badge_color changes" do
        expect(RegenerateUserIdentityImagesWorker).not_to receive(:perform_async)

        role.update!(badge_color: 'SILVER')
      end

      it "should not trigger identity image regeneration when badge_ring changes" do
        expect(RegenerateUserIdentityImagesWorker).not_to receive(:perform_async)

        role.update!(badge_ring: false)
      end
    end

    context "when role has no users" do
      it "should not trigger any workers when no users have this role" do
        empty_role = FactoryBot.create(:role)

        expect(RegenerateUserIdentityImagesWorker).not_to receive(:perform_async)

        empty_role.update!(name: "Updated Empty Role Name")
      end
    end

    context "when role changes affect parent_circle_level or purview_level" do
      it "should trigger identity image regeneration when parent_circle_level changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user1.id)
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user2.id)

        role.update!(parent_circle_level: :district)
      end

      it "should trigger identity image regeneration when purview_level changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user1.id)
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user2.id)

        role.update!(purview_level: :district)
      end
    end
  end
end

