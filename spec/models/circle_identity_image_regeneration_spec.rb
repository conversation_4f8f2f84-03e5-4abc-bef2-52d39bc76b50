require 'rails_helper'

RSpec.describe Circle, type: :model do
  describe "#trigger_user_identity_images_regeneration" do
    let(:circle) { FactoryBot.create(:circle, name: "Test Circle", short_name: "TC") }
    let(:user1) { FactoryBot.create(:user) }
    let(:user2) { FactoryBot.create(:user) }
    let(:role) { FactoryBot.create(:role) }

    before do
      # Clear any existing jobs
      Sidekiq::Worker.clear_all
    end

    context "when circle is used as parent_circle" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, parent_circle: circle) }
      let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, parent_circle: circle) }

      it "should trigger identity image regeneration when circle name changes" do
        expect {
          circle.update!(name: "Updated Circle Name")
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id, user2.id)
      end

      it "should trigger identity image regeneration when circle short_name changes" do
        expect {
          circle.update!(short_name: "UTC")
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id, user2.id)
      end

      it "should log the regeneration trigger" do
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for users with circle #{circle.id} due to circle name/short_name change")

        circle.update!(name: "Updated Circle Name")
      end
    end

    context "when circle is used as purview_circle" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, purview_circle: circle) }
      let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, purview_circle: circle) }

      it "should trigger identity image regeneration when circle name changes" do
        expect {
          circle.update!(name: "Updated Purview Circle")
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id, user2.id)
      end
    end

    context "when circle is used in badge_text through badge_icon_group" do
      let(:badge_icon_group) { FactoryBot.create(:badge_icon_group, circle: circle) }
      let(:badge_icon) { FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group) }
      let(:role_with_badge_text) { FactoryBot.create(:role, display_name_order: "role,badge_text") }
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role_with_badge_text, badge_icon: badge_icon) }

      it "should trigger identity image regeneration when circle name changes" do
        expect {
          circle.update!(name: "Updated Badge Circle")
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2) # One for parent/purview, one for badge_text

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to include(user1.id)
      end

      it "should log both types of regeneration triggers" do
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for users with circle #{circle.id} due to circle name/short_name change")
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for users with badge_text circle #{circle.id} due to circle name/short_name change")

        circle.update!(name: "Updated Badge Circle")
      end
    end

    context "when circle has no associated user roles" do
      it "should not trigger any workers" do
        expect {
          circle.update!(name: "Updated Unused Circle")
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end

      it "should not log regeneration triggers" do
        expect(Rails.logger).not_to receive(:info)

        circle.update!(name: "Updated Unused Circle")
      end
    end

    context "when non-badge description fields change" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, parent_circle: circle) }

      it "should not trigger identity image regeneration when description changes" do
        expect {
          circle.update!(description: "New description")
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end

      it "should not trigger identity image regeneration when level changes" do
        expect {
          circle.update!(level: :district)
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end

      it "should not trigger identity image regeneration when active status changes" do
        expect {
          circle.update!(active: false)
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end
    end

    context "with large number of users" do
      before do
        # Create many users with roles using this circle
        50.times do |i|
          user = FactoryBot.create(:user, name: "User #{i}")
          FactoryBot.create(:user_role, user: user, role: role, parent_circle: circle)
        end
      end

      it "should handle batch processing efficiently" do
        expect {
          circle.update!(name: "Updated Circle for Many Users")
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(50)
      end

      it "should not cause performance issues" do
        start_time = Time.current
        circle.update!(name: "Performance Test Circle")
        end_time = Time.current
        
        # Should complete within reasonable time (adjust threshold as needed)
        expect(end_time - start_time).to be < 5.seconds
      end
    end

    context "integration with existing callbacks" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, parent_circle: circle) }

      it "should trigger identity image regeneration along with other callbacks" do
        # Mock other callback methods to verify they still work
        allow_any_instance_of(Circle).to receive(:index_for_search)
        allow_any_instance_of(Circle).to receive(:flush_cache)
        allow_any_instance_of(Circle).to receive(:alert_on_name_change_circle_with_roles)

        expect_any_instance_of(Circle).to receive(:index_for_search)
        expect_any_instance_of(Circle).to receive(:flush_cache)
        expect_any_instance_of(Circle).to receive(:alert_on_name_change_circle_with_roles)
        expect_any_instance_of(Circle).to receive(:trigger_user_identity_images_regeneration)

        circle.update!(name: "Integration Test Circle")
      end
    end
  end
end
