require 'rails_helper'

RSpec.describe BadgeIcon, type: :model do
  describe "#trigger_user_identity_images_regeneration" do
    let(:circle) { FactoryBot.create(:circle) }
    let(:badge_icon_group) { FactoryBot.create(:badge_icon_group, circle: circle) }
    let(:admin_medium) { FactoryBot.create(:admin_medium) }
    let(:badge_icon) { FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group, admin_medium: admin_medium) }
    let(:user1) { FactoryBot.create(:user) }
    let(:user2) { FactoryBot.create(:user) }
    let(:role) { FactoryBot.create(:role) }

    before do
      # Clear any existing jobs
      Sidekiq::Worker.clear_all
    end

    context "when badge icon has associated user roles" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, badge_icon: badge_icon) }
      let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, badge_icon: badge_icon) }

      it "should trigger identity image regeneration when admin_medium_id changes" do
        new_admin_medium = FactoryBot.create(:admin_medium)
        
        expect {
          badge_icon.update!(admin_medium: new_admin_medium)
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id, user2.id)
      end

      it "should trigger identity image regeneration when color changes" do
        expect {
          badge_icon.update!(color: 'SILVER')
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id, user2.id)
      end

      it "should log the regeneration trigger" do
        new_admin_medium = FactoryBot.create(:admin_medium)
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for users with badge_icon #{badge_icon.id} due to badge icon changes")

        badge_icon.update!(admin_medium: new_admin_medium)
      end
    end

    context "when badge icon has no associated user roles" do
      it "should not trigger any workers" do
        new_admin_medium = FactoryBot.create(:admin_medium)
        
        expect {
          badge_icon.update!(admin_medium: new_admin_medium)
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end

      it "should not log regeneration triggers" do
        expect(Rails.logger).not_to receive(:info)

        badge_icon.update!(color: 'SILVER')
      end
    end

    context "when non-badge affecting fields change" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, badge_icon: badge_icon) }

      it "should not trigger identity image regeneration when badge_icon_group_id changes" do
        new_badge_icon_group = FactoryBot.create(:badge_icon_group)
        
        expect {
          badge_icon.update!(badge_icon_group: new_badge_icon_group)
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end
    end

    context "with large number of users" do
      before do
        # Create many users with roles using this badge icon
        25.times do |i|
          user = FactoryBot.create(:user, name: "User #{i}")
          FactoryBot.create(:user_role, user: user, role: role, badge_icon: badge_icon)
        end
      end

      it "should handle batch processing efficiently" do
        new_admin_medium = FactoryBot.create(:admin_medium)
        
        expect {
          badge_icon.update!(admin_medium: new_admin_medium)
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(25)
      end
    end
  end
end

RSpec.describe BadgeIconGroup, type: :model do
  describe "#trigger_user_identity_images_regeneration" do
    let(:circle1) { FactoryBot.create(:circle, name: "Circle 1") }
    let(:circle2) { FactoryBot.create(:circle, name: "Circle 2") }
    let(:badge_icon_group) { FactoryBot.create(:badge_icon_group, circle: circle1) }
    let(:badge_icon) { FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group) }
    let(:user1) { FactoryBot.create(:user) }
    let(:user2) { FactoryBot.create(:user) }
    let(:role) { FactoryBot.create(:role) }

    before do
      # Clear any existing jobs
      Sidekiq::Worker.clear_all
    end

    context "when badge icon group has associated user roles" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, badge_icon: badge_icon) }
      let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, badge_icon: badge_icon) }

      it "should trigger identity image regeneration when circle_id changes" do
        expect {
          badge_icon_group.update!(circle: circle2)
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id, user2.id)
      end

      it "should log the regeneration trigger" do
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for users with badge_icon_group #{badge_icon_group.id} due to circle association change")

        badge_icon_group.update!(circle: circle2)
      end
    end

    context "when badge icon group has no associated user roles" do
      it "should not trigger any workers" do
        expect {
          badge_icon_group.update!(circle: circle2)
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end

      it "should not log regeneration triggers" do
        expect(Rails.logger).not_to receive(:info)

        badge_icon_group.update!(circle: circle2)
      end
    end

    context "when non-badge affecting fields change" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, badge_icon: badge_icon) }

      it "should not trigger identity image regeneration when name changes" do
        expect {
          badge_icon_group.update!(name: "New Group Name")
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end
    end

    context "with large number of users" do
      before do
        # Create many users with roles using this badge icon group
        30.times do |i|
          user = FactoryBot.create(:user, name: "User #{i}")
          badge_icon = FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group)
          FactoryBot.create(:user_role, user: user, role: role, badge_icon: badge_icon)
        end
      end

      it "should handle batch processing efficiently" do
        expect {
          badge_icon_group.update!(circle: circle2)
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(30)
      end
    end

    context "when circle is set to nil" do
      let!(:user_role1) { FactoryBot.create(:user_role, user: user1, role: role, badge_icon: badge_icon) }

      it "should trigger identity image regeneration when circle is removed" do
        expect {
          badge_icon_group.update!(circle: nil)
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

        job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
        expect(job_args).to contain_exactly(user1.id)
      end
    end
  end
end
