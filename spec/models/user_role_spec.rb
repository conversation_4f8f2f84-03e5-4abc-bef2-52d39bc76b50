require 'rails_helper'

RSpec.describe UserRole, type: :model do
  # pending "add some examples to (or delete) #{__FILE__}"

  describe UserRole, :type => :model do
    context 'validate model ' do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "is valid with valid attributes" do
        expect(@user_role).to be_valid
      end

      it "invalid without a user_id" do
        @user_role.user_id = nil
        expect(@user_role).not_to be_valid
      end

      it "invalid without a role_id" do
        @user_role.role_id = nil
        expect(@user_role).not_to be_valid
      end

      it "valid with a parent_circle_id if role don't have parent circle id" do
        expect(@user_role).to be_valid
      end

      it "invalid with a parent_circle_id if role has parent_circle_id" do
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, parent_circle_id: @circle.id, parent_circle_level: nil)
        @user_role = FactoryBot.create(:user_role, role_id: @role.id, parent_circle_id: nil)
        @user_role.parent_circle_id = @circle.id
        expect(@user_role).not_to be_valid
      end
    end

  end

  describe "validate user_id" do
    context "it is valid with digits only" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "user_id valid with digits" do
        @user = FactoryBot.create(:user)
        @user_role.user_id = @user.id
        expect(@user_role).to be_valid
      end

      it "invalid with not existed user id" do
        @user_role.user_id = 100
        expect(@user_role).not_to be_valid
      end

      it "user_id not valid with english letters" do
        @user_role.user_id = "MLA OFFICE"
        expect(@user_role).not_to be_valid
      end

      it "not valid with alphanumeric" do
        @user_role.user_id = "123a"
        expect(@user_role).not_to be_valid
      end

      it "not valid with special characters" do
        @user_role.user_id = "123@"
        expect(@user_role).not_to be_valid
      end

      it "not valid with blank" do
        @user_role.user_id = ""
        expect(@user_role).not_to be_valid
      end

      it "not valid with empty space" do
        @user_role.user_id = " "
        expect(@user_role).not_to be_valid
      end
    end
  end

  describe "validate role_id" do
    context "valid with digits only" do
      before :each do
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "valid with digits" do
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)
        @user_role.role_id = @role.id
        @user_role.purview_circle_id = @purview_circle.id
        expect(@user_role).to be_valid
      end

      it "invalid with not existed role_id" do
        @user_role.role_id = 0
        expect(@user_role).not_to be_valid
      end

      it "not valid with english letters" do
        @user_role.role_id = "MLA OFFICE"
        expect(@user_role).not_to be_valid
      end

      it "not valid with alphanumeric" do
        @user_role.role_id = "123a"
        expect(@user_role).not_to be_valid
      end

      it "not valid with special characters" do
        @user_role.role_id = "123@"
        expect(@user_role).not_to be_valid
      end

      it "not valid with blank" do
        @user_role.role_id = ""
        expect(@user_role).not_to be_valid
      end

      it "not valid with empty space" do
        @user_role.role_id = " "
        expect(@user_role).not_to be_valid
      end
    end
  end

  describe "validate parent_circle_id" do
    context "valid with digits only" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "valid with digits" do
        @user_role.parent_circle_id = @parent_circle.id
        expect(@user_role).to be_valid
      end

      it "invalid with not existed parent_circle_id" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role = UserRole.create(user: @user, role: @role, parent_circle_id: -1)
        expect(@user_role).not_to be_valid
      end

      it "not valid with english letters" do
        @user_role.parent_circle_id = "MLA OFFICE"
        expect(@user_role).not_to be_valid
      end

      it "not valid with alphanumeric" do
        @user_role.parent_circle_id = "123a"
        expect(@user_role).not_to be_valid
      end

      it "not valid with special characters" do
        @user_role.parent_circle_id = "1237@"
        expect(@user_role).not_to be_valid
      end

      it "not valid with blank" do
        @user_role.parent_circle_id = ""
        expect(@user_role).not_to be_valid
      end

      it "not valid with empty space" do
        @user_role.parent_circle_id = " "
        expect(@user_role).not_to be_valid
      end

      it "if role has parent circle id then parent circle id should be blank" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: nil,
                                  parent_circle_id: @parent_circle.id,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true)
        @user_role = FactoryBot.create(:user_role, role_id: @role.id,
                                       parent_circle_id: nil)
        @user_role.parent_circle_id = @parent_circle.id
        expect(@user_role).not_to be_valid
      end
    end

    context "should be valid with given parent circle level of that role" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end

      it 'should not be valid with given primary circle level' do
        @user_role.role.parent_circle_level = :village
        expect(@user_role).not_to be_valid
      end
    end
  end

  describe "validate purview_circle_id" do
    context "valid with digits only" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @purview_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true)
        @user_role = FactoryBot.create(:user_role, role: @role, parent_circle_id: @parent_circle.id,
                                       purview_circle_id: @purview_circle.id)
      end
      it "valid with digits" do
        @user_role.purview_circle_id = @purview_circle.id
        expect(@user_role).to be_valid
      end

      it "invalid with not existed purview_circle_id" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role = UserRole.create(user: @user, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: -1)
        expect(@user_role).not_to be_valid
      end

      it "not valid with english letters" do
        @user_role.purview_circle_id = "MLA OFFICE"
        expect(@user_role).not_to be_valid
      end

      it "not valid with alphanumeric" do
        @user_role.purview_circle_id = "123a"
        expect(@user_role).not_to be_valid
      end

      it "not valid with special characters" do
        @user_role.purview_circle_id = "1237@"
        expect(@user_role).not_to be_valid
      end

    end

    context "should be valid with given purview circle level of that role" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @purview_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true)
        @user_role = FactoryBot.create(:user_role, role: @role, parent_circle_id: @parent_circle.id,
                                       purview_circle_id: @purview_circle.id)
      end
      it 'should be valid with given secondary circle level' do
        expect(@user_role).to be_valid
      end
    end

    context "purview circle id validation based on role" do
      it "should be valid if has purview circle is false in role" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true)

        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       purview_circle_id: nil)
        expect(@user_role).to be_valid
      end
      it "should be valid if has purview circle is true in role" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @parent_circle = FactoryBot.create(:circle,
                                           name: Faker::Name.unique.name,
                                           name_en: Faker::Name.unique.name,
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 100,
                                                circle_type: :location,
                                                level: :state)
        @district_level_circle = FactoryBot.create(:circle,
                                                   name: Faker::Name.unique.name,
                                                   name_en: Faker::Name.unique.name,
                                                   active: true,
                                                   members_count: 100,
                                                   circle_type: :location,
                                                   parent_circle_id: @state_level_circle.id,
                                                   level: :district)
        @purview_circle = FactoryBot.create(:circle,
                                            name: Faker::Name.unique.name,
                                            name_en: Faker::Name.unique.name,
                                            active: true,
                                            members_count: 100,
                                            circle_type: :location,
                                            parent_circle_id: @district_level_circle.id,
                                            level: :mandal)
        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       purview_circle_id: @purview_circle.id)
        expect(@user_role).to be_valid
      end
      it "should be invalid if has purview circle is false in role and purview circle id is present" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true)

        @parent_circle = FactoryBot.create(:circle,
                                           name: Faker::Name.unique.name,
                                           name_en: Faker::Name.unique.name,
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id)
        @user_role.purview_circle_id = @parent_circle.id
        expect(@user_role).not_to be_valid

      end
      it "should be invalid if role purview level and purview circle id level is not matched" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true)

        @parent_circle = FactoryBot.create(:circle,
                                           name: Faker::Name.unique.name,
                                           name_en: Faker::Name.unique.name,
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 100,
                                                circle_type: :location,
                                                level: :state)
        @purview_circle = FactoryBot.create(:circle,
                                            name: Faker::Name.unique.name,
                                            name_en: Faker::Name.unique.name,
                                            active: true,
                                            members_count: 100,
                                            circle_type: :location,
                                            parent_circle_id: @state_level_circle.id,
                                            level: :district)
        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        # expect to raise record invalid error
        expect { FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                   purview_circle_id: @purview_circle.id) }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
  end

  describe "validate grade level" do
    context "validate the overrideable grade level" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "is valid without a grade_level" do
        @user_role.grade_level = nil
        expect(@user_role).to be_valid
      end

      it "should be overrideable when grade level for a role is already there" do
        @user_role.grade_level = :grade_2
        expect(@user_role.grade_level).to eq("grade_2")
      end
    end
  end

  describe "validate badge ring" do
    context "validate the overrideable badge ring" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "is valid without a badge_ring" do
        @user_role.badge_ring = nil
        expect(@user_role).to be_valid
      end

      it "should be overrideable when badge_ring for a role is already there" do
        @user_role.badge_ring = false
        expect(@user_role.badge_ring).to eq(false)
      end
    end
  end

  describe "validate badge color" do
    context "validate the overrideable badge color" do
      before :each do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
      end
      it "is valid without a badge_color" do
        @user_role.badge_color = nil
        expect(@user_role).to be_valid
      end

      it "should be overrideable when badge color for a role is already there" do
        @user_role.badge_color = :SILVER
        expect(@user_role.badge_color).to eq("SILVER")
      end
    end
  end

  describe "validate quota value of a circle" do
    context "absolute value check of user roles limit" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       purview_circle_id: @purview_circle.id)
      end
      it "should be valid" do
        expect(@user_role).to be_valid
      end

      it "should be valid with the quota value" do
        @user1 = FactoryBot.create(:user)
        @user_role1 = FactoryBot.create(:user_role, user: @user1, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
        expect(@user_role1).to be_valid
      end

      it "should not be valid once quota limit reaches" do
        @user1 = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user2 = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role1 = FactoryBot.create(:user_role, user: @user1, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
        @user_role2 = UserRole.create(user: @user2, role: @role, parent_circle_id: @parent_circle.id)
        expect(@user_role2).not_to be_valid
      end
    end

    context "no limit value check of user roles" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  quota_value: nil,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
      end
      it "should be valid" do
        expect(@user_role).to be_valid
      end

      it "should be valid with the unlimited user roles addition" do
        @user1 = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role1 = FactoryBot.create(:user_role, user: @user1, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
        expect(@user_role1).to be_valid
      end
    end

    context "percentage check of user roles limit" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
      end
      it "should be valid" do
        expect(@user_role).to be_valid
      end

      it "should be valid with the quota value" do
        @user1 = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role1 = FactoryBot.create(:user_role, user: @user1, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
        expect(@user_role1).to be_valid
      end

      it "should not be valid once quota limit reaches" do
        @user1 = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user2 = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @user_role1 = FactoryBot.create(:user_role, user: @user1, role: @role, parent_circle_id: @parent_circle.id, purview_circle_id: @purview_circle.id)
        @user_role2 = UserRole.create(user: @user2, role: @role, parent_circle_id: @parent_circle.id)
        expect(@user_role2).not_to be_valid
      end
    end
  end

  describe "validate primary role" do
    context "should be a boolean value" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       primary_role: true, purview_circle_id: @purview_circle.id)
      end
      it "should be valid" do
        expect(@user_role).to be_valid
      end

      it "should not be valid" do
        @user_role.primary_role = nil
        expect(@user_role).not_to be_valid
      end
    end

    context "validate primary role based on whether user had already a primary role" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @role1 = FactoryBot.create(:role,
                                   name: Faker::Name.unique.name,
                                   has_badge: true,
                                   badge_ring: true,
                                   badge_color: :GOLD,
                                   quota_type: :percentage,
                                   quota_value: 2,
                                   grade_level: :grade_2,
                                   parent_circle_level: :political_party,
                                   has_purview: true,
                                   purview_level: :mandal,
                                   active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @state1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state1.id)
        @purview_circle1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district1.id)
        @parent_circle1 = FactoryBot.create(:circle,
                                            name: "circle name1",
                                            name_en: "circle name EN1",
                                            active: true,
                                            members_count: 100,
                                            circle_type: :interest,
                                            level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       primary_role: true, purview_circle_id: @purview_circle.id)

        @user_role1 = FactoryBot.create(:user_role, user: @user, role: @role1, parent_circle_id: @parent_circle1.id,
                                        primary_role: true, purview_circle_id: @purview_circle1.id)

      end
      it "should be valid" do
        expect(@user_role1).to be_valid
      end
    end

    context "validate whether user had already a primary role when primary role is false" do
      it "should be valid" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @role1 = FactoryBot.create(:role,
                                   name: Faker::Name.unique.name,
                                   has_badge: true,
                                   badge_ring: true,
                                   badge_color: :GOLD,
                                   quota_type: :percentage,
                                   quota_value: 2,
                                   grade_level: :grade_2,
                                   parent_circle_level: :political_party,
                                   has_purview: true,
                                   purview_level: :mandal,
                                   active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @state1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state1.id)
        @purview_circle1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district1.id)
        @parent_circle1 = FactoryBot.create(:circle,
                                            name: "circle name1",
                                            name_en: "circle name EN1",
                                            active: true,
                                            members_count: 100,
                                            circle_type: :interest,
                                            level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       primary_role: true, purview_circle_id: @purview_circle.id)

        @user_role1 = FactoryBot.create(:user_role, user: @user, role: @role1, parent_circle_id: @parent_circle1.id,
                                        primary_role: false, purview_circle_id: @purview_circle1.id)
        expect(@user_role1).to be_valid
      end

      it "should not be valid" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)
        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)

        expect { @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                                primary_role: false, purview_circle_id: @purview_circle.id) }.to raise_error(ActiveRecord::RecordInvalid)
      end

      it "should be valid when has badge false with primary role false" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: false,
                                  badge_ring: nil,
                                  badge_color: nil,
                                  badge_icon_ribbon: nil,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @role1 = FactoryBot.create(:role,
                                   name: Faker::Name.unique.name,
                                   has_badge: false,
                                   badge_ring: nil,
                                   badge_color: nil,
                                   badge_icon_ribbon: nil,
                                   quota_type: :percentage,
                                   quota_value: 2,
                                   grade_level: :grade_2,
                                   parent_circle_level: :political_party,
                                   has_purview: true,
                                   purview_level: :mandal,
                                   active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name",
                                           name_en: "circle name EN",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)
        @state1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state1.id)
        @purview_circle1 = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district1.id)
        @parent_circle1 = FactoryBot.create(:circle,
                                            name: "circle name1",
                                            name_en: "circle name EN1",
                                            active: true,
                                            members_count: 100,
                                            circle_type: :interest,
                                            level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       primary_role: false, purview_circle_id: @purview_circle.id)

        @user_role1 = FactoryBot.create(:user_role, user: @user, role: @role1, parent_circle_id: @parent_circle1.id,
                                        primary_role: false, purview_circle_id: @purview_circle1.id)
        expect(@user_role1).to be_valid
      end

      it "should be invalid when has badge false with primary role true" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: false,
                                  badge_ring: nil,
                                  badge_color: nil,
                                  badge_icon_ribbon: nil,
                                  quota_type: :percentage,
                                  quota_value: 2,
                                  grade_level: :grade_2,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :mandal,
                                  active: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @state = FactoryBot.create(:circle, active: true, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, active: true, circle_type: :location, level: :district, parent_circle_id: @state.id)
        @purview_circle = FactoryBot.create(:circle, active: true, circle_type: :location, level: :mandal, parent_circle_id: @district.id)
        @parent_circle = FactoryBot.create(:circle,
                                           name: "circle name1",
                                           name_en: "circle name EN1",
                                           active: true,
                                           members_count: 100,
                                           circle_type: :interest,
                                           level: :political_party)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @parent_circle.id,
                                       primary_role: false, purview_circle_id: @purview_circle.id)
        @user_role.primary_role = true
        expect(@user_role).not_to be_valid
      end
    end
  end

  describe "validate user role" do
    context "start date" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      end
      it "should be valid if in date format" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.current)
        expect(@user_role).to be_valid
      end

      it "should not be valid if nil" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: nil)
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if in other format like string" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: "January")
        expect(@user_role).not_to be_valid
      end

    end

    context "end date" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      end
      it "should be valid if in date format" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.yesterday, end_date: Date.current)
        expect(@user_role).to be_valid
      end

      it "should be valid if nil" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      end_date: nil)
        expect(@user_role).to be_valid
      end

      it "should be default end date if input is nil" do
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                       end_date: nil)
        expect(@user_role.end_date).to eq(UserRole::DEFAULT_USER_ROLE_END_DATE)
      end

      it "should not save date if in other format like string" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      end_date: "January")
        expect(@user_role.end_date).to be_nil
      end
    end

    context "validate start date and end date" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      end
      it "should be valid if end date is ahead of start date" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.yesterday, end_date: Date.current)
        expect(@user_role).to be_valid
      end

      it "should not be valid if start date is ahead of end date" do
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.current, end_date: Date.yesterday)
        expect(@user_role).not_to be_valid
      end
    end
  end

  describe "validate no of user roles" do
    context "with duration change" do
      before :each do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user = FactoryBot.create(:user, name: Faker::Name.unique.name)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      end

      it "should be valid if no two user roles duration doesn't overlaps" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                          start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.parse('2016-01-01'), end_date: Date.parse('2019-12-31'))
        expect(@user_role).to be_valid
      end

      it "should not be valid if two user roles duration overlaps role2 between role1" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                          start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.parse('2021-01-01'), end_date: Date.parse('2023-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if two user roles duration overlaps role1 between role2" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                          start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
                                      start_date: Date.parse('2019-01-01'), end_date: Date.parse('2025-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if role2 start date between role1 duration" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2023-01-01'), end_date: Date.parse('2025-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if role2 end date between role1 duration" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2019-01-01'), end_date: Date.parse('2023-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if role1 start date between role2 duration" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2018-01-01'), end_date: Date.parse('2021-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if role1 end date between role2 duration" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: Date.parse('2024-01-01'))
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2023-01-01'), end_date: Date.parse('2025-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if two roles with end date nil" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: nil)
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2023-01-01'), end_date: nil)
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if role1 with end date nil and role2 start_date and end_date greater than role1 start_date" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: nil)
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2023-01-01'), end_date: Date.parse('2025-01-01'))
        expect(@user_role).not_to be_valid
      end

      it "should be valid if role1 with end date nil and role2 start_date and end_date lesser than role1 start_date" do
        FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2020-01-01'), end_date: nil)
        @user_role = FactoryBot.build(:user_role, user: @user, role: @role, parent_circle_id: @circle.id, start_date: Date.parse('2015-01-01'), end_date: Date.parse('2019-01-01'))
        expect(@user_role).to be_valid
      end
    end
  end

  describe "validate badge_icon_id" do
    context "when badge_icon_id is present for user role where role has badge" do
      it "should be valid" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
        @user_role.badge_icon_id = nil
        expect(@user_role).to be_valid
      end

      it "should not be valid if badge icon present but badge icon url is nil" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id)
        @user_role.badge_icon_id = -1
        expect(@user_role).not_to be_valid
      end

      it "should not be valid if role has no badge" do
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: false,
                                  badge_ring: nil,
                                  badge_color: nil,
                                  badge_icon_ribbon: nil)
        @photo = FactoryBot.create(:photo)
        @admin_user = FactoryBot.create(:admin_user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: @circle.id)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                        badge_icon_group_id: @badge_icon_group.id)
        @user_role = FactoryBot.create(:user_role, role_id: @role.id, parent_circle_id: @circle.id, primary_role: false)
        @user_role.badge_icon_id = @badge_icon.id
        expect(@user_role).not_to be_valid
      end
    end
  end

  describe "get badge color" do
    context "check whether the proper badge color is coming" do
      it "should return the badge color of overrided user role badge color" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, parent_circle_id: @parent_circle.id, badge_color: :SILVER)
        expect(@user_role.get_badge_color).to eq("SILVER")
      end

      it "should return the badge color of role badge color" do
        @parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, badge_color: :WHITE)
        @user_role = FactoryBot.create(:user_role, role: @role, parent_circle_id: @parent_circle.id)
        expect(@user_role.get_badge_color).to eq("WHITE")
      end
    end
  end

  describe "validate show on about page" do
    context "user can show only max of 5 user roles on about page" do
      it "should not be valid if user has more than 5 user roles on show_about_page" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
                                       start_date: Date.parse("2010-01-01"),
                                       end_date: Date.parse("2012-01-01"), show_on_about_page: true)
        @user_role2 = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
                                        start_date: Date.parse("2012-01-02"),
                                        end_date: Date.parse("2014-01-01"), show_on_about_page: true)
        @user_role3 = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id, start_date: Date.parse("2014-01-02"), end_date: Date.parse("2016-01-01"), show_on_about_page: true)
        @user_role4 = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id, start_date: Date.parse("2016-01-02"), end_date: Date.parse("2018-01-01"), show_on_about_page: true)
        @user_role5 = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id, start_date: Date.parse("2018-01-02"), end_date: Date.parse("2020-01-01"), show_on_about_page: true)
        @user_role6 = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id, start_date: Date.parse("2020-01-02"), end_date: Date.parse("2022-01-01"), show_on_about_page: true)
        expect(@user_role6).not_to be_valid
      end
    end
  end

  describe 'test get_badge_ring' do
    context 'should return which type of ring' do
      it 'should return gold color of badge ring' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
                                       badge_color: :GOLD)
        expect(@user_role.get_badge_ring).to eq('GOLD_RING')
      end

      it 'should return silver color of badge ring' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
                                       badge_color: :SILVER)
        expect(@user_role.get_badge_ring).to eq('SILVER_RING')
      end

      it 'should return no color of badge ring' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, badge_ring: false)
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       badge_color: :WHITE)
        expect(@user_role.get_badge_ring).to eq('NO_RING')
      end

    end
  end

  describe 'get badge banner text' do
    context 'when user role has free text' do
      it "should return free text" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member', has_free_text: true)
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       free_text: 'Free Text')
        expect(@user_role.get_description).to eq('Free Text')
      end
    end

    context 'should return badge banner text based on role name, parent circle to badge text and purview circle to
             badge text' do
      it 'return a badge text' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member', display_name_order: "role")
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id)
        expect(@user_role.get_description).to eq('Member')
      end

      it 'return a badge text with parent circle to badge text when parent circle is interest' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member', display_name_order: "parent,role")
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id)
        expect(@user_role.get_description).to eq(@circle.name + ' Member')
      end

      it 'return a badge text with badge_icon circle to badge text' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member', parent_circle_level: :political_party, parent_circle_id: nil,
                                  has_purview: true,
                                  purview_level: :state, display_name_order: 'role,purview_suffix,badge_text')
        @purview_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @admin_user = FactoryBot.create(:admin_user)
        @badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: @circle.id)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                        badge_icon_group_id: @badge_icon_group.id)
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       badge_icon_id: @badge_icon.id, purview_circle_id: @purview_circle.id)
        expect(@user_role.get_description).to eq('Member రాష్ట్ర ' + @circle.name)
      end

      it 'return a badge text with purview circle to badge text when purview circle is location' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @location_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @role = FactoryBot.create(:role, name: 'Member', has_purview: true, purview_level: :state,
                                  display_name_order: "purview,role")
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       purview_circle_id: @location_circle.id)
        expect(@user_role.get_description).to eq(@location_circle.name + ' Member')
      end
    end
  end

  describe 'test get_badge_user_affiliated_location_circle_id' do
    context 'it returns badge user affiliated location circle id' do
      it 'should return badge user affiliated location circle id when purview circle is location' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @location_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @role = FactoryBot.create(:role, name: 'Member', has_purview: true, purview_level: :state,
                                  purview_circle_to_badge_text: true, prefix: true)
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       purview_circle_id: @location_circle.id)
        expect(@user_role.get_badge_user_affiliated_location_circle_id).to eq(@location_circle.id)
      end
    end
  end

  describe 'test get_readable_grade_level' do
    context 'it returns readable grade level' do
      it 'should return readable grade level from user role' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
                                       grade_level: :grade_1)
        expect(@user_role.get_readable_grade_level).to eq(1)
      end

      it 'should return readable grade level from role when user role grade level is nil' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member', grade_level: :grade_1)
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       grade_level: nil)
        expect(@user_role.get_readable_grade_level).to eq(1)
      end
    end
  end

  describe 'test get_grade_level' do
    context 'it returns grade level' do
      it 'should return grade level from user role' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
                                       grade_level: :grade_1)
        expect(@user_role.get_grade_level).to eq("grade_1")
      end

      it 'should return grade level from role when user role grade level is nil' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member', grade_level: :grade_1)
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
                                       grade_level: nil)
        expect(@user_role.get_grade_level).to eq("grade_1")
      end
    end
  end

  describe 'get_badge_role_header_for_badge_celebration' do
    context 'it returns badge role header for badge celebration' do
      it 'should return badge role header for badge celebration' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @role = FactoryBot.create(:role, name: 'Member')
        @user_role = FactoryBot.create(:user_role, role: @role, user: @user, parent_circle_id: @circle.id,
        )
        expect(@user_role.get_badge_role_header_for_badge_celebration).to eq("Member")
      end
    end
  end

  describe 'get_sub_header_for_badge_celebration' do
    context 'it returns sub header for badge celebration' do
      it 'should return sub header for badge celebration' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
        )
        expect(@user_role.get_sub_header_for_badge_celebration).to eq("")
      end
    end
  end

  describe 're_index_user_invites_after_user_badge_update' do
    context 'it re-indexes user invites after user badge update' do
      it 'should re-index user invites of user role related user after user role update' do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id,
        )
        @user_role.re_index_user_invites_after_user_badge_update
        expect(UpdateUserInvitesIndex.jobs.last['args']).to eq([@user.id])
      end
    end
  end

  describe "#get_description" do
    context "get description when short_name is not present" do
      it "should return role description" do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true,
                                  display_name_order: "parent,role,purview",
                                  badge_icon_ribbon: true)
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle1.id, purview_circle_id: @circle.id)
        expected_description = "#{@circle1.name} #{@role.name} #{@circle.name}"
        expect(@user_role.get_description).to eq(expected_description)
      end
    end

    context "get description when short_name is present" do
      it "should return role description" do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true,
                                  display_name_order: "parent,role,purview",
                                  badge_icon_ribbon: true)
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party, short_name: "short_name")
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle1.id, purview_circle_id: @circle.id)
        expected_description = "#{@circle1.short_name} #{@role.name} #{@circle.name}"
        expect(@user_role.get_description).to eq(expected_description)
      end
    end
  end

  describe "#get_role_name" do
    context "show role name based on end date" do
      it "should return role name with Ex-" do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role,
                                  name: "Minister",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true,
                                  display_name_order: "parent,role,purview",
                                  badge_icon_ribbon: true)
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party, short_name: "short_name")
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle1.id, purview_circle_id: @circle.id,
                                       end_date: Date.yesterday)
        expect(@user_role.get_role_name).to eq("Ex-Minister")
      end
      it "should return role name with మాజీ " do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role,
                                  name: "ఉపముఖ్యమంత్రి",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true,
                                  display_name_order: "parent,role,purview",
                                  badge_icon_ribbon: true)
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party, short_name: "short_name")
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle1.id, purview_circle_id: @circle.id,
                                       end_date: Date.yesterday)
        expect(@user_role.get_role_name).to eq("మాజీ ఉపముఖ్యమంత్రి")
      end
      it "should return role name without Ex- or మాజీ " do
        @user = FactoryBot.create(:user)
        @role = FactoryBot.create(:role,
                                  name: "Minister",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: true,
                                  purview_level: :state,
                                  active: true,
                                  display_name_order: "parent,role,purview",
                                  badge_icon_ribbon: true)
        @circle = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @circle1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party, short_name: "short_name")
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle1.id, purview_circle_id: @circle.id)
        expect(@user_role.get_role_name).to eq("Minister")
      end
    end
  end

  describe '#update_floww_lead_score' do
    context 'when active status changes' do
      before :each do
        @user = FactoryBot.create(:user)
        @user_role = FactoryBot.create(:user_role, user: @user, active: true)
      end
      it 'triggers UpdateLeadScore' do
        allow(UpdateLeadScore).to receive(:perform_async)
        @user_role.update(active: false)
        expect(UpdateLeadScore).to have_received(:perform_async).with(@user.id)
      end
    end

    context 'when active status does not change' do
      before :each do
        @user = FactoryBot.create(:user)
        @user_role = FactoryBot.create(:user_role, user: @user, active: true)
      end
      it 'does not trigger UpdateLeadScore' do
        allow(UpdateLeadScore).to receive(:perform_async)
        @user_role.update(badge_color: 'GOLD')
        expect(UpdateLeadScore).not_to have_received(:perform_async)
      end
    end
  end

  describe "#trigger_identity_image_regeneration" do
    let(:user) { FactoryBot.create(:user) }
    let(:parent_circle) { FactoryBot.create(:circle, circle_type: :interest, level: :political_party) }
    let(:purview_circle) { FactoryBot.create(:circle, circle_type: :location, level: :state) }
    let(:role) { FactoryBot.create(:role, has_purview: true, purview_level: :state) }
    let(:user_role) { FactoryBot.create(:user_role, user: user, role: role, parent_circle_id: parent_circle.id, purview_circle_id: purview_circle.id) }

    context "when badge description affecting fields change" do
      it "should trigger identity image regeneration when role_id changes" do
        new_role = FactoryBot.create(:role)

        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(role: new_role)
      end

      it "should trigger identity image regeneration when parent_circle_id changes" do
        new_parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(parent_circle_id: new_parent_circle.id)
      end

      it "should trigger identity image regeneration when purview_circle_id changes" do
        new_purview_circle = FactoryBot.create(:circle, circle_type: :location, level: :state)

        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(purview_circle_id: new_purview_circle.id)
      end

      it "should trigger identity image regeneration when free_text changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(free_text: "New free text")
      end

      it "should trigger identity image regeneration when display_name_order changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(display_name_order: "role,parent,purview")
      end

      it "should trigger identity image regeneration when badge_icon_id changes" do
        badge_icon = FactoryBot.create(:badge_icon)

        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(badge_icon: badge_icon)
      end

      it "should trigger identity image regeneration when active status changes" do
        expect(RegenerateUserIdentityImagesWorker).to receive(:perform_async).with(user.id)

        user_role.update!(active: false)
      end

      it "should log the regeneration trigger" do
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for user #{user.id} due to badge description change")

        user_role.update!(free_text: "New free text")
      end
    end

    context "when non-badge description fields change" do
      it "should not trigger identity image regeneration when badge_color changes" do
        expect(RegenerateUserIdentityImagesWorker).not_to receive(:perform_async)

        user_role.update!(badge_color: 'SILVER')
      end

      it "should not trigger identity image regeneration when badge_ring changes" do
        expect(RegenerateUserIdentityImagesWorker).not_to receive(:perform_async)

        user_role.update!(badge_ring: false)
      end
    end
  end
end
