require 'rails_helper'

RSpec.describe UserPosterLayout, type: :model do
  describe UserPosterLayout, :type => :model do
    context 'validate UserPosterLayout' do
      before :each do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, h1_count: 0, h2_count: 0)
      end
      it 'should be valid' do
        expect(@user_poster_layout).to be_valid
      end
      it 'should be invalid without h1_count' do
        @user_poster_layout.h1_count = nil
        expect(@user_poster_layout).to_not be_valid
      end
      it 'should be invalid without h2_count' do
        @user_poster_layout.h2_count = nil
        expect(@user_poster_layout).to_not be_valid
      end
    end
  end

  describe '#validate_user_poster_layout' do
    context 'validate layout' do
      before :each do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, h1_count: 0, h2_count: 0)
      end

      it 'should be valid' do
        expect(@user_poster_layout).to be_valid
      end

      it 'should be invalid with layout not supported' do
        @user_poster_layout.h1_count = 3
        @user_poster_layout.h2_count = 2
        expect(@user_poster_layout).to_not be_valid
      end
    end
  end

  describe '#set_wati_msg_key_on_layout_creation' do
    context 'when entity is a User and has never subscribed' do
      it 'sends a Wati message' do
        user = FactoryBot.create(:user)
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).and_return(false)
        FactoryBot.create(:user_poster_layout, entity: user)
        expect(UserMetadatum.where(user_id: user.id, key: Constants.send_wati_msg_key).exists?).to be_truthy
      end
    end

    context 'when entity is a User and has subscribed' do
      it 'does not send a Wati message' do
        user = FactoryBot.create(:user)
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).and_return(true)
        FactoryBot.build(:user_poster_layout, entity: user)
        expect(UserMetadatum.where(user_id: user.id, key: Constants.send_wati_msg_key).exists?).to be_falsey
      end
    end

    context 'when entity is not a User' do
      it 'does not send a Wati message' do
        non_user_entity = FactoryBot.create(:circle)
        FactoryBot.build(:user_poster_layout, entity: non_user_entity)
        expect(UserMetadatum.where(user_id: non_user_entity.id, key: Constants.send_wati_msg_key).exists?)
          .to be_falsey
      end
    end
  end

  describe "#get_latest_remark_of_layout" do
    let(:user_poster_layout) { create(:user_poster_layout) }

    it "returns the latest remark if remarks are present" do
      FactoryBot.create(:poster_layout_remark, user_poster_layout: user_poster_layout, remarks: "First remark")
      FactoryBot.create(:poster_layout_remark, user_poster_layout: user_poster_layout, remarks: "Latest remark")
      result = user_poster_layout.get_latest_remark_of_layout
      expect(result).to eq("Latest remark")
    end

    it "returns nil if no remarks are present" do
      result = user_poster_layout.get_latest_remark_of_layout
      expect(result).to be_nil
    end
  end

  describe "#trigger_identity_image_regeneration" do
    let(:user) { FactoryBot.create(:user) }
    let(:circle) { FactoryBot.create(:circle) }

    before do
      # Clear any existing jobs
      Sidekiq::Worker.clear_all
    end

    context 'when entity is a User' do
      it 'should trigger identity image regeneration on create' do
        expect {
          FactoryBot.create(:user_poster_layout, entity: user, h1_count: 1, h2_count: 1)
        }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

        job = RegenerateUserIdentityImagesWorker.jobs.last
        expect(job['args']).to eq([user.id])
      end

      it 'should log the regeneration trigger' do
        allow(RegenerateUserIdentityImagesWorker).to receive(:perform_async)
        expect(Rails.logger).to receive(:info).with("Identity image regeneration triggered for user #{user.id} due to new UserPosterLayout creation")

        FactoryBot.create(:user_poster_layout, entity: user, h1_count: 1, h2_count: 1)
      end
    end

    context 'when entity is not a User' do
      it 'should not trigger identity image regeneration' do
        expect {
          FactoryBot.create(:user_poster_layout, entity: circle, h1_count: 1, h2_count: 1)
        }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
      end

      it 'should not log regeneration trigger' do
        expect(Rails.logger).not_to receive(:info)
        expect(RegenerateUserIdentityImagesWorker).not_to receive(:perform_async)

        FactoryBot.create(:user_poster_layout, entity: circle, h1_count: 1, h2_count: 1)
      end
    end
  end
end
